#!/usr/bin/env python3
"""
Debug script to investigate overhaul overdue dashboard issue.
"""

import sys
import os
import sqlite3
from datetime import datetime, date

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_overhaul_dashboard():
    """Debug the overhaul dashboard issue."""
    try:
        # Import after adding to path
        import config
        from models import Overhaul, Equipment
        import utils
        
        print("=== OVERHAUL DASHBOARD DEBUG ===")
        print(f"Current date: {date.today()}")
        print()
        
        # Check database connection
        db_path = config.get_db_path()
        print(f"Database path: {db_path}")
        
        if not os.path.exists(db_path):
            print("❌ Database does not exist!")
            return
        
        # Get all overhauls
        print("\n1. CHECKING ALL OVERHAULS:")
        print("-" * 50)
        overhauls = Overhaul.get_all()
        print(f"Total overhauls found: {len(overhauls)}")
        
        if not overhauls:
            print("❌ No overhauls found in database!")
            return
        
        # Show first few overhauls
        print("\nFirst 5 overhauls:")
        for i, oh in enumerate(overhauls[:5]):
            print(f"  {i+1}. ID: {oh.get('overhaul_id')}, Type: {oh.get('overhaul_type')}, "
                  f"Due: {oh.get('due_date')}, Done: {oh.get('done_date')}, Status: {oh.get('status')}")
        
        # Check overdue count using dashboard logic
        print("\n2. DASHBOARD OVERDUE COUNT CALCULATION:")
        print("-" * 50)
        
        overdue_count = 0
        oh1_overdue = 0
        oh2_overdue = 0
        other_overdue = 0
        
        for oh in overhauls:
            equipment_id = oh.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id) if equipment_id else None
            overhaul_type = oh.get('overhaul_type', 'Unknown')
            
            # Calculate status using centralized logic
            status = utils.calculate_overhaul_status(
                oh.get('overhaul_type'),
                oh.get('due_date'),
                oh.get('done_date'),
                equipment.date_of_commission if equipment else None,
                None,  # oh1_done_date - will be calculated internally if needed
                None,  # custom_intervals
                equipment.meterage_kms if equipment else None
            )
            
            print(f"Overhaul {oh.get('overhaul_id')} ({overhaul_type}): "
                  f"Due={oh.get('due_date')}, Done={oh.get('done_date')}, "
                  f"Calculated Status={status}")
            
            # Count only overdue overhauls (past due date or over meterage limit)
            if status == "overdue":
                overdue_count += 1
                # Track by overhaul type for detailed logging
                if overhaul_type == 'OH-I':
                    oh1_overdue += 1
                elif overhaul_type == 'OH-II':
                    oh2_overdue += 1
                else:
                    other_overdue += 1
        
        print(f"\nOverdue breakdown: OH-I={oh1_overdue}, OH-II={oh2_overdue}, Other={other_overdue}")
        print(f"Total overdue count: {overdue_count}")
        
        # Check specific overdue cases
        print("\n3. DETAILED OVERDUE ANALYSIS:")
        print("-" * 50)
        
        today = date.today()
        for oh in overhauls:
            due_date_str = oh.get('due_date')
            done_date_str = oh.get('done_date')
            
            if due_date_str and not done_date_str:
                try:
                    due_date = date.fromisoformat(due_date_str)
                    days_overdue = (today - due_date).days
                    
                    if days_overdue > 0:
                        print(f"  OVERDUE: {oh.get('overhaul_type')} for equipment {oh.get('equipment_id')}")
                        print(f"    Due: {due_date_str}, Days overdue: {days_overdue}")
                        print(f"    Equipment: {oh.get('make_and_type')} (BA: {oh.get('ba_number')})")
                        
                except Exception as e:
                    print(f"  ERROR parsing date for overhaul {oh.get('overhaul_id')}: {e}")
        
        # Check database directly
        print("\n4. DIRECT DATABASE QUERY:")
        print("-" * 50)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check overhauls table structure
        cursor.execute("PRAGMA table_info(overhauls)")
        columns = cursor.fetchall()
        print("Overhauls table columns:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # Query overdue overhauls directly
        cursor.execute("""
            SELECT overhaul_id, equipment_id, overhaul_type, due_date, done_date, status
            FROM overhauls 
            WHERE done_date IS NULL OR done_date = '' OR done_date = 'None'
            ORDER BY due_date
        """)
        
        incomplete_overhauls = cursor.fetchall()
        print(f"\nIncomplete overhauls (no done_date): {len(incomplete_overhauls)}")
        
        for oh in incomplete_overhauls:
            overhaul_id, equipment_id, overhaul_type, due_date, done_date, status = oh
            if due_date:
                try:
                    due_date_obj = date.fromisoformat(due_date)
                    days_diff = (today - due_date_obj).days
                    if days_diff > 0:
                        print(f"  OVERDUE: {overhaul_type} (ID: {overhaul_id}) - {days_diff} days overdue")
                except:
                    print(f"  Invalid date: {overhaul_type} (ID: {overhaul_id}) - Due: {due_date}")
        
        conn.close()
        
        print("\n=== DEBUG COMPLETE ===")
        
    except Exception as e:
        print(f"Error in debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_overhaul_dashboard()
