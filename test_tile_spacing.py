#!/usr/bin/env python3
"""
Test script to verify tile spacing logic for dashboard tiles.
"""

def calculate_spacing(title):
    """Calculate spacing based on title length - matches the logic in ModernDashboardTile"""
    title_length = len(title)
    if title_length > 20:  # For very long titles like "Battery Replacement Due" (23 chars)
        return 18
    elif title_length > 17:  # For long titles like "Maintenance Overdue" (19 chars)
        return 14
    elif title_length > 15:  # For medium titles like "Overhaul Overdue" (16 chars)
        return 10
    else:  # For shorter titles like "Total Equipment" (15 chars)
        return 6

def main():
    titles = [
        "Total Equipment",
        "Maintenance Overdue", 
        "Tyre Rotation Due",
        "Battery Replacement Due",
        "Overhaul Overdue"
    ]
    
    print("Dashboard Tile Spacing Analysis:")
    print("=" * 50)
    
    for title in titles:
        length = len(title)
        spacing = calculate_spacing(title)
        print(f"{title:<25} | {length:2d} chars | {spacing:2d}px spacing")
    
    print("\nExpected behavior:")
    print("- Battery Replacement Due should have the most spacing (18px)")
    print("- Maintenance Overdue should have 14px spacing")
    print("- All tiles should display without text overlap")

if __name__ == "__main__":
    main()
