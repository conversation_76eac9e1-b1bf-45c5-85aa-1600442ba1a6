import sqlite3
from config import get_db_path

def fix_kpl_data():
    """Fix the problematic KPL data in the database."""
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("Fixing KPL data...")
    
    # Check current problematic data
    cursor.execute("SELECT ba_number, srtr_ltr_hr, rd_towing_ltr_100km FROM equipment WHERE rd_towing_ltr_100km > 1000")
    problematic_rows = cursor.fetchall()
    
    print(f"Found {len(problematic_rows)} rows with problematic RD/TOWING values")
    
    for row in problematic_rows:
        ba_number, srtr, rd_towing = row
        print(f"  {ba_number}: SRTR={srtr}, RD/TOWING={rd_towing}")
        
        # Try to fix the RD/TOWING value
        # If it's 6898, it might be "68" and "98" concatenated
        # Let's assume it should be 68 for now
        if rd_towing == 6898.0:
            new_rd_towing = 68.0
            print(f"    Fixing {ba_number}: {rd_towing} -> {new_rd_towing}")
            
            cursor.execute(
                "UPDATE equipment SET rd_towing_ltr_100km = ? WHERE ba_number = ?",
                (new_rd_towing, ba_number)
            )
    
    # Commit changes
    print(f"Committing {len(problematic_rows)} updates...")
    conn.commit()
    print("Changes committed successfully!")
    
    # Verify the fix
    print("\nVerifying the fix...")
    cursor.execute("SELECT ba_number, srtr_ltr_hr, rd_towing_ltr_100km FROM equipment LIMIT 10")
    fixed_rows = cursor.fetchall()
    
    # Also check if any problematic values still exist
    cursor.execute("SELECT COUNT(*) FROM equipment WHERE rd_towing_ltr_100km > 1000")
    remaining_problems = cursor.fetchone()[0]
    print(f"Remaining problematic values: {remaining_problems}")
    
    print("\nAfter fix - Sample data:")
    for row in fixed_rows:
        ba_number, srtr, rd_towing = row
        print(f"  {ba_number}: {srtr:.0f}/{rd_towing:.0f}")
    
    conn.close()
    print("KPL data fix completed!")

if __name__ == "__main__":
    fix_kpl_data()
